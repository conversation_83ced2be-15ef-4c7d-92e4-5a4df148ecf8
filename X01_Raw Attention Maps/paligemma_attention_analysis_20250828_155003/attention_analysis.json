{"model_info": {"num_layers": 26, "layer_names": ["language_layer_0", "language_layer_1", "language_layer_2", "language_layer_3", "language_layer_4", "language_layer_5", "language_layer_6", "language_layer_7", "language_layer_8", "language_layer_9", "language_layer_10", "language_layer_11", "language_layer_12", "language_layer_13", "language_layer_14", "language_layer_15", "language_layer_16", "language_layer_17", "language_layer_18", "language_layer_19", "language_layer_20", "language_layer_21", "language_layer_22", "language_layer_23", "language_layer_24", "language_layer_25"]}, "input_info": {"image_path": "/home/<USER>/dataset/X/avatar.png", "text_prompt": "where is the forest", "num_image_patches": 256, "text_tokens": ["<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<bos>", "where", "▁is", "▁the", "▁forest", "\n"]}, "layer_statistics": {"language_layer_0": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.040557861328125, "max": 0.748046875, "min": 3.5762786865234375e-07}, "attention_patterns": {"image_to_image": {"mean": 0.003376007080078125, "std": 0.0406494140625, "max": 0.748046875}, "image_to_text": {"mean": 0.02264404296875, "std": 0.0408935546875, "max": 0.439453125}, "text_to_image": {"mean": 0.0010690689086914062, "std": 0.005481719970703125, "max": 0.1300048828125}, "text_to_text": {"mean": 0.12103271484375, "std": 0.1583251953125, "max": 0.57470703125}}}, "language_layer_1": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.031829833984375, "max": 0.8017578125, "min": 4.5299530029296875e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0018548965454101562, "std": 0.0128021240234375, "max": 0.429931640625}, "image_to_text": {"mean": 0.0875244140625, "std": 0.172119140625, "max": 0.65673828125}, "text_to_image": {"mean": 0.0010538101196289062, "std": 0.0016736984252929688, "max": 0.0176239013671875}, "text_to_text": {"mean": 0.1217041015625, "std": 0.219482421875, "max": 0.8017578125}}}, "language_layer_2": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.03936767578125, "max": 0.732421875, "min": 6.794929504394531e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0013561248779296875, "std": 0.00936126708984375, "max": 0.252685546875}, "image_to_text": {"mean": 0.1087646484375, "std": 0.23095703125, "max": 0.732421875}, "text_to_image": {"mean": 0.000988006591796875, "std": 0.0017261505126953125, "max": 0.0302886962890625}, "text_to_text": {"mean": 0.12451171875, "std": 0.1578369140625, "max": 0.6201171875}}}, "language_layer_3": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.042388916015625, "max": 0.7841796875, "min": 1.7881393432617188e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0012159347534179688, "std": 0.007083892822265625, "max": 0.288330078125}, "image_to_text": {"mean": 0.11474609375, "std": 0.25341796875, "max": 0.76806640625}, "text_to_image": {"mean": 0.0009083747863769531, "std": 0.0015630722045898438, "max": 0.020233154296875}, "text_to_text": {"mean": 0.1279296875, "std": 0.2052001953125, "max": 0.7841796875}}}, "language_layer_4": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.03271484375, "max": 0.6513671875, "min": 5.4836273193359375e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0014505386352539062, "std": 0.007411956787109375, "max": 0.2423095703125}, "image_to_text": {"mean": 0.104736328125, "std": 0.1851806640625, "max": 0.6513671875}, "text_to_image": {"mean": 0.00099945068359375, "std": 0.0016193389892578125, "max": 0.026214599609375}, "text_to_text": {"mean": 0.1240234375, "std": 0.14208984375, "max": 0.57275390625}}}, "language_layer_5": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.025909423828125, "max": 0.50439453125, "min": 5.0067901611328125e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0019273757934570312, "std": 0.00565338134765625, "max": 0.2100830078125}, "image_to_text": {"mean": 0.08447265625, "std": 0.1463623046875, "max": 0.50439453125}, "text_to_image": {"mean": 0.0013408660888671875, "std": 0.0020427703857421875, "max": 0.0322265625}, "text_to_text": {"mean": 0.10943603515625, "std": 0.11505126953125, "max": 0.42724609375}}}, "language_layer_6": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0330810546875, "max": 0.6630859375, "min": 6.318092346191406e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0017337799072265625, "std": 0.00574493408203125, "max": 0.26318359375}, "image_to_text": {"mean": 0.09271240234375, "std": 0.1964111328125, "max": 0.6630859375}, "text_to_image": {"mean": 0.0009317398071289062, "std": 0.0024776458740234375, "max": 0.07452392578125}, "text_to_text": {"mean": 0.126953125, "std": 0.1409912109375, "max": 0.5048828125}}}, "language_layer_7": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0284271240234375, "max": 0.65869140625, "min": 1.0132789611816406e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0018720626831054688, "std": 0.00710296630859375, "max": 0.32080078125}, "image_to_text": {"mean": 0.0867919921875, "std": 0.16162109375, "max": 0.65869140625}, "text_to_image": {"mean": 0.0011644363403320312, "std": 0.001922607421875, "max": 0.0273284912109375}, "text_to_text": {"mean": 0.11700439453125, "std": 0.1380615234375, "max": 0.49169921875}}}, "language_layer_8": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0245513916015625, "max": 0.67919921875, "min": 1.6093254089355469e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002239227294921875, "std": 0.005191802978515625, "max": 0.1484375}, "image_to_text": {"mean": 0.07110595703125, "std": 0.1427001953125, "max": 0.67919921875}, "text_to_image": {"mean": 0.0014257431030273438, "std": 0.002429962158203125, "max": 0.034454345703125}, "text_to_text": {"mean": 0.1058349609375, "std": 0.1534423828125, "max": 0.53759765625}}}, "language_layer_9": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0258026123046875, "max": 0.60888671875, "min": 5.960464477539062e-07}, "attention_patterns": {"image_to_image": {"mean": 0.002105712890625, "std": 0.003749847412109375, "max": 0.117919921875}, "image_to_text": {"mean": 0.0767822265625, "std": 0.1517333984375, "max": 0.60888671875}, "text_to_image": {"mean": 0.0018939971923828125, "std": 0.00383758544921875, "max": 0.1109619140625}, "text_to_text": {"mean": 0.08587646484375, "std": 0.1441650390625, "max": 0.485107421875}}}, "language_layer_10": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.021697998046875, "max": 0.5732421875, "min": 2.9206275939941406e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0023975372314453125, "std": 0.005756378173828125, "max": 0.16455078125}, "image_to_text": {"mean": 0.06439208984375, "std": 0.1236572265625, "max": 0.5732421875}, "text_to_image": {"mean": 0.001735687255859375, "std": 0.0055084228515625, "max": 0.120849609375}, "text_to_text": {"mean": 0.09259033203125, "std": 0.1234130859375, "max": 0.4716796875}}}, "language_layer_11": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0270233154296875, "max": 0.6220703125, "min": 1.0132789611816406e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0020294189453125, "std": 0.00496673583984375, "max": 0.254150390625}, "image_to_text": {"mean": 0.080078125, "std": 0.1583251953125, "max": 0.6220703125}, "text_to_image": {"mean": 0.0015201568603515625, "std": 0.002773284912109375, "max": 0.058837890625}, "text_to_text": {"mean": 0.101806640625, "std": 0.1212158203125, "max": 0.396728515625}}}, "language_layer_12": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0247650146484375, "max": 0.63671875, "min": 1.2516975402832031e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002285003662109375, "std": 0.00749969482421875, "max": 0.209228515625}, "image_to_text": {"mean": 0.06915283203125, "std": 0.142333984375, "max": 0.63671875}, "text_to_image": {"mean": 0.0019321441650390625, "std": 0.0030975341796875, "max": 0.0465087890625}, "text_to_text": {"mean": 0.084228515625, "std": 0.1014404296875, "max": 0.345458984375}}}, "language_layer_13": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.02435302734375, "max": 0.60888671875, "min": 1.7285346984863281e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00213623046875, "std": 0.005367279052734375, "max": 0.231689453125}, "image_to_text": {"mean": 0.0755615234375, "std": 0.1397705078125, "max": 0.60888671875}, "text_to_image": {"mean": 0.0017948150634765625, "std": 0.004367828369140625, "max": 0.07421875}, "text_to_text": {"mean": 0.090087890625, "std": 0.1107177734375, "max": 0.420166015625}}}, "language_layer_14": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0276947021484375, "max": 0.61279296875, "min": 2.384185791015625e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0020809173583984375, "std": 0.005397796630859375, "max": 0.275634765625}, "image_to_text": {"mean": 0.077880859375, "std": 0.164306640625, "max": 0.61279296875}, "text_to_image": {"mean": 0.0021038055419921875, "std": 0.0037670135498046875, "max": 0.0870361328125}, "text_to_text": {"mean": 0.076904296875, "std": 0.11993408203125, "max": 0.39892578125}}}, "language_layer_15": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0280303955078125, "max": 0.70654296875, "min": 4.76837158203125e-07}, "attention_patterns": {"image_to_image": {"mean": 0.002071380615234375, "std": 0.00714111328125, "max": 0.2259521484375}, "image_to_text": {"mean": 0.07830810546875, "std": 0.163330078125, "max": 0.70654296875}, "text_to_image": {"mean": 0.0016422271728515625, "std": 0.0028476715087890625, "max": 0.053314208984375}, "text_to_text": {"mean": 0.09661865234375, "std": 0.12164306640625, "max": 0.458251953125}}}, "language_layer_16": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.026824951171875, "max": 0.7431640625, "min": 8.344650268554688e-07}, "attention_patterns": {"image_to_image": {"mean": 0.002185821533203125, "std": 0.006801605224609375, "max": 0.44091796875}, "image_to_text": {"mean": 0.07342529296875, "std": 0.15771484375, "max": 0.7431640625}, "text_to_image": {"mean": 0.0024700164794921875, "std": 0.0050506591796875, "max": 0.09014892578125}, "text_to_text": {"mean": 0.06121826171875, "std": 0.10791015625, "max": 0.36328125}}}, "language_layer_17": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.02801513671875, "max": 0.62109375, "min": 5.960464477539062e-07}, "attention_patterns": {"image_to_image": {"mean": 0.002063751220703125, "std": 0.00782012939453125, "max": 0.1962890625}, "image_to_text": {"mean": 0.07861328125, "std": 0.162109375, "max": 0.62109375}, "text_to_image": {"mean": 0.0021495819091796875, "std": 0.006500244140625, "max": 0.127685546875}, "text_to_text": {"mean": 0.074951171875, "std": 0.11578369140625, "max": 0.392822265625}}}, "language_layer_18": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0306396484375, "max": 0.69775390625, "min": 7.152557373046875e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0018892288208007812, "std": 0.00778961181640625, "max": 0.28125}, "image_to_text": {"mean": 0.0860595703125, "std": 0.177734375, "max": 0.69775390625}, "text_to_image": {"mean": 0.001613616943359375, "std": 0.00396728515625, "max": 0.07958984375}, "text_to_text": {"mean": 0.0977783203125, "std": 0.1651611328125, "max": 0.5341796875}}}, "language_layer_19": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.03515625, "max": 0.75048828125, "min": 3.5762786865234375e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0015554428100585938, "std": 0.00601959228515625, "max": 0.261962890625}, "image_to_text": {"mean": 0.100341796875, "std": 0.208740234375, "max": 0.75048828125}, "text_to_image": {"mean": 0.0016260147094726562, "std": 0.004474639892578125, "max": 0.11285400390625}, "text_to_text": {"mean": 0.0972900390625, "std": 0.1241455078125, "max": 0.425048828125}}}, "language_layer_20": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.03955078125, "max": 0.79296875, "min": 1.2516975402832031e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0012454986572265625, "std": 0.00392913818359375, "max": 0.1727294921875}, "image_to_text": {"mean": 0.113525390625, "std": 0.236328125, "max": 0.79296875}, "text_to_image": {"mean": 0.0015172958374023438, "std": 0.00384521484375, "max": 0.07061767578125}, "text_to_text": {"mean": 0.1019287109375, "std": 0.180908203125, "max": 0.61767578125}}}, "language_layer_21": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.04400634765625, "max": 0.88623046875, "min": 1.7881393432617188e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0010232925415039062, "std": 0.0038299560546875, "max": 0.1783447265625}, "image_to_text": {"mean": 0.123046875, "std": 0.265625, "max": 0.88623046875}, "text_to_image": {"mean": 0.0014562606811523438, "std": 0.00336456298828125, "max": 0.066162109375}, "text_to_text": {"mean": 0.10455322265625, "std": 0.150634765625, "max": 0.5595703125}}}, "language_layer_22": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.04742431640625, "max": 0.94287109375, "min": 1.1920928955078125e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0008244514465332031, "std": 0.0027828216552734375, "max": 0.2264404296875}, "image_to_text": {"mean": 0.1314697265625, "std": 0.287109375, "max": 0.94287109375}, "text_to_image": {"mean": 0.0016546249389648438, "std": 0.00313568115234375, "max": 0.06365966796875}, "text_to_text": {"mean": 0.0960693359375, "std": 0.1925048828125, "max": 0.6201171875}}}, "language_layer_23": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.0452880859375, "max": 0.85986328125, "min": 1.0728836059570312e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0009241104125976562, "std": 0.0029087066650390625, "max": 0.1966552734375}, "image_to_text": {"mean": 0.127197265625, "std": 0.2734375, "max": 0.85986328125}, "text_to_image": {"mean": 0.0018167495727539062, "std": 0.00366973876953125, "max": 0.06817626953125}, "text_to_text": {"mean": 0.08917236328125, "std": 0.171875, "max": 0.5693359375}}}, "language_layer_24": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.046661376953125, "max": 0.8828125, "min": 8.940696716308594e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0008349418640136719, "std": 0.002429962158203125, "max": 0.1488037109375}, "image_to_text": {"mean": 0.1309814453125, "std": 0.282470703125, "max": 0.8828125}, "text_to_image": {"mean": 0.0016307830810546875, "std": 0.0036220550537109375, "max": 0.05621337890625}, "text_to_text": {"mean": 0.09710693359375, "std": 0.1412353515625, "max": 0.49609375}}}, "language_layer_25": {"matrix_shape": [262, 262], "total_sequence_length": 262, "num_image_patches": 256, "num_text_tokens": 262, "overall_stats": {"mean": 0.0038166046142578125, "std": 0.02789306640625, "max": 0.62890625, "min": 3.635883331298828e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0019359588623046875, "std": 0.005802154541015625, "max": 0.307861328125}, "image_to_text": {"mean": 0.08404541015625, "std": 0.161865234375, "max": 0.62890625}, "text_to_image": {"mean": 0.0015048980712890625, "std": 0.0034008026123046875, "max": 0.075439453125}, "text_to_text": {"mean": 0.10247802734375, "std": 0.1107177734375, "max": 0.39306640625}}}}}