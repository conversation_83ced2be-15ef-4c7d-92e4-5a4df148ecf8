{"model_info": {"num_layers": 26, "layer_names": ["language_layer_0", "language_layer_1", "language_layer_2", "language_layer_3", "language_layer_4", "language_layer_5", "language_layer_6", "language_layer_7", "language_layer_8", "language_layer_9", "language_layer_10", "language_layer_11", "language_layer_12", "language_layer_13", "language_layer_14", "language_layer_15", "language_layer_16", "language_layer_17", "language_layer_18", "language_layer_19", "language_layer_20", "language_layer_21", "language_layer_22", "language_layer_23", "language_layer_24", "language_layer_25"]}, "input_info": {"image_path": "/home/<USER>/dataset/X/coke.png", "text_prompt": "pick coke can", "num_image_patches": 256, "text_tokens": ["<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<bos>", "pick", "▁coke", "▁can", "\n"]}, "layer_statistics": {"language_layer_0": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.041290283203125, "max": 0.7470703125, "min": 5.960464477539063e-08}, "attention_patterns": {"image_to_image": {"mean": 0.00334930419921875, "std": 0.041107177734375, "max": 0.7470703125}, "image_to_text": {"mean": 0.028533935546875, "std": 0.051605224609375, "max": 0.444091796875}, "text_to_image": {"mean": 0.00130462646484375, "std": 0.006290435791015625, "max": 0.1319580078125}, "text_to_text": {"mean": 0.1331787109375, "std": 0.1702880859375, "max": 0.57080078125}}}, "language_layer_1": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0304718017578125, "max": 0.7939453125, "min": 3.2186508178710938e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00196075439453125, "std": 0.01367950439453125, "max": 0.42919921875}, "image_to_text": {"mean": 0.099609375, "std": 0.1707763671875, "max": 0.6083984375}, "text_to_image": {"mean": 0.0012369155883789062, "std": 0.00177764892578125, "max": 0.0162200927734375}, "text_to_text": {"mean": 0.13671875, "std": 0.2261962890625, "max": 0.7939453125}}}, "language_layer_2": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.038787841796875, "max": 0.72509765625, "min": 4.172325134277344e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00142669677734375, "std": 0.0095062255859375, "max": 0.249267578125}, "image_to_text": {"mean": 0.126953125, "std": 0.2423095703125, "max": 0.72509765625}, "text_to_image": {"mean": 0.0011119842529296875, "std": 0.001743316650390625, "max": 0.0259552001953125}, "text_to_text": {"mean": 0.14306640625, "std": 0.2174072265625, "max": 0.6923828125}}}, "language_layer_3": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.040557861328125, "max": 0.7685546875, "min": 2.384185791015625e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0013675689697265625, "std": 0.0084075927734375, "max": 0.321533203125}, "image_to_text": {"mean": 0.1300048828125, "std": 0.2578125, "max": 0.7685546875}, "text_to_image": {"mean": 0.0009927749633789062, "std": 0.0016193389892578125, "max": 0.016937255859375}, "text_to_text": {"mean": 0.149169921875, "std": 0.2227783203125, "max": 0.75927734375}}}, "language_layer_4": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0310211181640625, "max": 0.67236328125, "min": 3.814697265625e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0016088485717773438, "std": 0.007659912109375, "max": 0.214599609375}, "image_to_text": {"mean": 0.11761474609375, "std": 0.1845703125, "max": 0.654296875}, "text_to_image": {"mean": 0.0011224746704101562, "std": 0.0015821456909179688, "max": 0.014129638671875}, "text_to_text": {"mean": 0.1424560546875, "std": 0.169189453125, "max": 0.67236328125}}}, "language_layer_5": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0244598388671875, "max": 0.55224609375, "min": 4.351139068603516e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002193450927734375, "std": 0.006374359130859375, "max": 0.186279296875}, "image_to_text": {"mean": 0.08770751953125, "std": 0.1480712890625, "max": 0.55224609375}, "text_to_image": {"mean": 0.00164794921875, "std": 0.0024166107177734375, "max": 0.046630859375}, "text_to_text": {"mean": 0.11566162109375, "std": 0.1376953125, "max": 0.5146484375}}}, "language_layer_6": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.028411865234375, "max": 0.6064453125, "min": 4.589557647705078e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00201416015625, "std": 0.005786895751953125, "max": 0.1995849609375}, "image_to_text": {"mean": 0.096923828125, "std": 0.1781005859375, "max": 0.6064453125}, "text_to_image": {"mean": 0.0012674331665039062, "std": 0.0028781890869140625, "max": 0.07244873046875}, "text_to_text": {"mean": 0.1351318359375, "std": 0.1380615234375, "max": 0.5126953125}}}, "language_layer_7": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0276641845703125, "max": 0.68115234375, "min": 6.556510925292969e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0019626617431640625, "std": 0.00677490234375, "max": 0.346435546875}, "image_to_text": {"mean": 0.0994873046875, "std": 0.168212890625, "max": 0.68115234375}, "text_to_image": {"mean": 0.0014467239379882812, "std": 0.0024890899658203125, "max": 0.041717529296875}, "text_to_text": {"mean": 0.1259765625, "std": 0.15576171875, "max": 0.50244140625}}}, "language_layer_8": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.021942138671875, "max": 0.65087890625, "min": 1.3113021850585938e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0024356842041015625, "std": 0.00543212890625, "max": 0.20263671875}, "image_to_text": {"mean": 0.0753173828125, "std": 0.134765625, "max": 0.65087890625}, "text_to_image": {"mean": 0.0017385482788085938, "std": 0.0032482147216796875, "max": 0.041412353515625}, "text_to_text": {"mean": 0.1109619140625, "std": 0.156005859375, "max": 0.61279296875}}}, "language_layer_9": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0225830078125, "max": 0.572265625, "min": 5.364418029785156e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0023651123046875, "std": 0.0038299560546875, "max": 0.07867431640625}, "image_to_text": {"mean": 0.07891845703125, "std": 0.141845703125, "max": 0.572265625}, "text_to_image": {"mean": 0.00209808349609375, "std": 0.00440216064453125, "max": 0.1260986328125}, "text_to_text": {"mean": 0.092529296875, "std": 0.136962890625, "max": 0.470703125}}}, "language_layer_10": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0211029052734375, "max": 0.537109375, "min": 5.125999450683594e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00241851806640625, "std": 0.004810333251953125, "max": 0.1556396484375}, "image_to_text": {"mean": 0.07611083984375, "std": 0.12890625, "max": 0.53369140625}, "text_to_image": {"mean": 0.0019330978393554688, "std": 0.006641387939453125, "max": 0.16650390625}, "text_to_text": {"mean": 0.10101318359375, "std": 0.1312255859375, "max": 0.537109375}}}, "language_layer_11": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.020050048828125, "max": 0.464599609375, "min": 3.2186508178710938e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002468109130859375, "std": 0.0047607421875, "max": 0.2208251953125}, "image_to_text": {"mean": 0.0736083984375, "std": 0.121826171875, "max": 0.464599609375}, "text_to_image": {"mean": 0.0018949508666992188, "std": 0.003040313720703125, "max": 0.053924560546875}, "text_to_text": {"mean": 0.10302734375, "std": 0.10723876953125, "max": 0.35546875}}}, "language_layer_12": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0217132568359375, "max": 0.59130859375, "min": 2.7418136596679688e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0024662017822265625, "std": 0.007266998291015625, "max": 0.2313232421875}, "image_to_text": {"mean": 0.07366943359375, "std": 0.130615234375, "max": 0.59130859375}, "text_to_image": {"mean": 0.002071380615234375, "std": 0.002960205078125, "max": 0.033843994140625}, "text_to_text": {"mean": 0.09393310546875, "std": 0.101318359375, "max": 0.359375}}}, "language_layer_13": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.02239990234375, "max": 0.59912109375, "min": 1.4901161193847656e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002239227294921875, "std": 0.004901885986328125, "max": 0.17041015625}, "image_to_text": {"mean": 0.0853271484375, "std": 0.135009765625, "max": 0.59912109375}, "text_to_image": {"mean": 0.0018510818481445312, "std": 0.004119873046875, "max": 0.08673095703125}, "text_to_text": {"mean": 0.105224609375, "std": 0.11138916015625, "max": 0.377685546875}}}, "language_layer_14": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0257568359375, "max": 0.638671875, "min": 8.344650268554688e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0021572113037109375, "std": 0.00514984130859375, "max": 0.2349853515625}, "image_to_text": {"mean": 0.089599609375, "std": 0.1617431640625, "max": 0.638671875}, "text_to_image": {"mean": 0.0021877288818359375, "std": 0.004192352294921875, "max": 0.08489990234375}, "text_to_text": {"mean": 0.0880126953125, "std": 0.0985107421875, "max": 0.3876953125}}}, "language_layer_15": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0264739990234375, "max": 0.70361328125, "min": 1.4901161193847656e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00218963623046875, "std": 0.006412506103515625, "max": 0.1629638671875}, "image_to_text": {"mean": 0.087890625, "std": 0.1654052734375, "max": 0.70361328125}, "text_to_image": {"mean": 0.0017070770263671875, "std": 0.0035457611083984375, "max": 0.099609375}, "text_to_text": {"mean": 0.11260986328125, "std": 0.135009765625, "max": 0.482421875}}}, "language_layer_16": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0238037109375, "max": 0.7900390625, "min": 2.0265579223632812e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00238800048828125, "std": 0.00714874267578125, "max": 0.47705078125}, "image_to_text": {"mean": 0.07769775390625, "std": 0.1473388671875, "max": 0.7900390625}, "text_to_image": {"mean": 0.002651214599609375, "std": 0.0053253173828125, "max": 0.10064697265625}, "text_to_text": {"mean": 0.06427001953125, "std": 0.09307861328125, "max": 0.337158203125}}}, "language_layer_17": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.028717041015625, "max": 0.6376953125, "min": 2.2649765014648438e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00196075439453125, "std": 0.00738525390625, "max": 0.2421875}, "image_to_text": {"mean": 0.099609375, "std": 0.17724609375, "max": 0.6376953125}, "text_to_image": {"mean": 0.0022792816162109375, "std": 0.006809234619140625, "max": 0.1376953125}, "text_to_text": {"mean": 0.083251953125, "std": 0.102294921875, "max": 0.36279296875}}}, "language_layer_18": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.032135009765625, "max": 0.69775390625, "min": 1.9669532775878906e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0018367767333984375, "std": 0.00746917724609375, "max": 0.311279296875}, "image_to_text": {"mean": 0.10595703125, "std": 0.2020263671875, "max": 0.69775390625}, "text_to_image": {"mean": 0.0020046234130859375, "std": 0.00492095947265625, "max": 0.12420654296875}, "text_to_text": {"mean": 0.097412109375, "std": 0.155517578125, "max": 0.521484375}}}, "language_layer_19": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.036224365234375, "max": 0.78271484375, "min": 1.2516975402832031e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0015344619750976562, "std": 0.00603485107421875, "max": 0.375}, "image_to_text": {"mean": 0.12139892578125, "std": 0.2310791015625, "max": 0.78271484375}, "text_to_image": {"mean": 0.0018892288208007812, "std": 0.005786895751953125, "max": 0.1715087890625}, "text_to_text": {"mean": 0.103271484375, "std": 0.127685546875, "max": 0.42626953125}}}, "language_layer_20": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.037445068359375, "max": 0.74853515625, "min": 2.4437904357910156e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0014200210571289062, "std": 0.004413604736328125, "max": 0.1322021484375}, "image_to_text": {"mean": 0.1273193359375, "std": 0.2393798828125, "max": 0.74853515625}, "text_to_image": {"mean": 0.0017442703247070312, "std": 0.004119873046875, "max": 0.089599609375}, "text_to_text": {"mean": 0.1107177734375, "std": 0.173828125, "max": 0.56982421875}}}, "language_layer_21": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.043975830078125, "max": 0.86474609375, "min": 2.384185791015625e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0010204315185546875, "std": 0.00385284423828125, "max": 0.2041015625}, "image_to_text": {"mean": 0.147705078125, "std": 0.283447265625, "max": 0.86474609375}, "text_to_image": {"mean": 0.00173187255859375, "std": 0.004062652587890625, "max": 0.10272216796875}, "text_to_text": {"mean": 0.111328125, "std": 0.1429443359375, "max": 0.45458984375}}}, "language_layer_22": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.046417236328125, "max": 0.9296875, "min": 5.960464477539063e-08}, "attention_patterns": {"image_to_image": {"mean": 0.00087738037109375, "std": 0.003078460693359375, "max": 0.334228515625}, "image_to_text": {"mean": 0.155029296875, "std": 0.300537109375, "max": 0.9296875}, "text_to_image": {"mean": 0.002166748046875, "std": 0.00439453125, "max": 0.113525390625}, "text_to_text": {"mean": 0.089111328125, "std": 0.1405029296875, "max": 0.4365234375}}}, "language_layer_23": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.043212890625, "max": 0.81787109375, "min": 1.8477439880371094e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0010423660278320312, "std": 0.00302886962890625, "max": 0.341796875}, "image_to_text": {"mean": 0.1466064453125, "std": 0.27880859375, "max": 0.81787109375}, "text_to_image": {"mean": 0.0021114349365234375, "std": 0.00321197509765625, "max": 0.037078857421875}, "text_to_text": {"mean": 0.09185791015625, "std": 0.1295166015625, "max": 0.4140625}}}, "language_layer_24": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.047607421875, "max": 0.8798828125, "min": 1.1920928955078125e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0007843971252441406, "std": 0.00250244140625, "max": 0.13818359375}, "image_to_text": {"mean": 0.1597900390625, "std": 0.308349609375, "max": 0.8798828125}, "text_to_image": {"mean": 0.0017271041870117188, "std": 0.0033473968505859375, "max": 0.0477294921875}, "text_to_text": {"mean": 0.111572265625, "std": 0.1309814453125, "max": 0.454833984375}}}, "language_layer_25": {"matrix_shape": [261, 261], "total_sequence_length": 261, "num_image_patches": 256, "num_text_tokens": 261, "overall_stats": {"mean": 0.0038318634033203125, "std": 0.0295562744140625, "max": 0.6923828125, "min": 6.854534149169922e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0018768310546875, "std": 0.0052032470703125, "max": 0.2236328125}, "image_to_text": {"mean": 0.10394287109375, "std": 0.185546875, "max": 0.6923828125}, "text_to_image": {"mean": 0.0017995834350585938, "std": 0.0027618408203125, "max": 0.03509521484375}, "text_to_text": {"mean": 0.10784912109375, "std": 0.10943603515625, "max": 0.358642578125}}}}}